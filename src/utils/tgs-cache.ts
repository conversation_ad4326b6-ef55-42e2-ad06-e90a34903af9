import { globalCache } from './cache-utils';

const CACHE_CONFIG = { duration: 30 * 60 * 1000 }; // 30 minutes

/**
 * Get cached TGS data (Lottie JSON) for a given URL
 */
export const getCachedTgsData = (tgsUrl: string): object | null => {
  return globalCache.get<object>(`tgs:${tgsUrl}`);
};

/**
 * Set cached TGS data (Lottie JSON) for a given URL
 */
export const setCachedTgsData = (tgsUrl: string, lottieJson: object): void => {
  globalCache.set(`tgs:${tgsUrl}`, lottie<PERSON><PERSON>, CACHE_CONFIG);
};

/**
 * Clear all cached TGS data
 */
export const clearTgsCache = (): void => {
  globalCache.invalidatePattern('tgs:');
};

/**
 * Get cache statistics for debugging
 */
export const getTgsCacheStats = () => {
  return {
    message: 'TGS cache now uses global cache manager',
    cacheDurationMinutes: CACHE_CONFIG.duration / (60 * 1000),
  };
};

/**
 * Clean up expired cache entries
 */
export const cleanupExpiredTgsCache = (): number => {
  globalCache.clearExpired();
  return 0; // Global cache handles cleanup internally
};

// Periodic cleanup is now handled by the global cache manager

// Development helper: expose cache functions to window for debugging
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  (window as typeof window & { tgsCache: unknown }).tgsCache = {
    getStats: getTgsCacheStats,
    clear: clearTgsCache,
    cleanup: cleanupExpiredTgsCache,
  };
}
