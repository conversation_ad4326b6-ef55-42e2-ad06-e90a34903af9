import type { DocumentSnapshot } from 'firebase/firestore';
import {
  collection,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import type { OrderEntity } from '@/core.constants';
// Cache will be managed by components that call these functions
import { firebaseFunctions, firestore } from '@/root-context';

const COLLECTION_NAME = 'orders';

export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';
  limit?: number;
  lastDoc?: DocumentSnapshot | null;
  currentUserId?: string;
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null;
  hasMore: boolean;
}

export interface MakePurchaseAsBuyerResponse {
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}

export const getOrdersForBuyers = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    // Cache will be handled by components using this function
    const pageSize = filters.limit ?? 20;
    console.log('🔍 getOrdersForBuyers called with filters:', filters);

    // For buyers tab: get orders where sellerId exists and buyerId is null/doesn't exist (sellers looking for buyers)
    let q = query(
      collection(firestore, COLLECTION_NAME),
      where('status', '==', 'active'),
      where('sellerId', '!=', null),
    );

    if (filters.collectionId) {
      q = query(q, where('collectionId', '==', filters.collectionId));
    }

    // Apply sorting
    if (filters.sortBy) {
      if (filters.sortBy === 'price_asc') {
        q = query(q, orderBy('amount', 'asc'));
      } else if (filters.sortBy === 'price_desc') {
        q = query(q, orderBy('amount', 'desc'));
      } else if (filters.sortBy === 'date_asc') {
        q = query(q, orderBy('createdAt', 'asc'));
      } else {
        q = query(q, orderBy('createdAt', 'desc'));
      }
    } else {
      q = query(q, orderBy('createdAt', 'desc'));
    }

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }
    q = query(q, limit(pageSize + 1)); // Get one extra to check if there are more

    const snapshot = await getDocs(q);
    let orders: OrderEntity[] = [];
    let lastDoc = null;
    let hasMore = false;

    const docs = snapshot.docs;
    let processedCount = 0;

    for (const element of docs) {
      const doc = element;

      if (processedCount >= pageSize) {
        hasMore = true; // There's at least one more document
        break;
      }

      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;

      // For buyers tab: only include orders where buyerId is null or doesn't exist
      // Also exclude orders where current user is the seller
      if (
        !orderData.buyerId &&
        (!filters.currentUserId || orderData.sellerId !== filters.currentUserId)
      ) {
        orders.push(orderData);
        lastDoc = doc; // Only set lastDoc when we actually include an order
        processedCount++;
      }
    }

    // Apply price filters client-side
    if (filters.minPrice !== undefined) {
      orders = orders.filter((order) => order.amount >= filters.minPrice!);
    }
    if (filters.maxPrice !== undefined) {
      orders = orders.filter((order) => order.amount <= filters.maxPrice!);
    }

    const result = {
      orders,
      lastDoc,
      hasMore,
    };

    // Cache will be handled by components using this function

    return result;
  } catch (error) {
    console.error('Error fetching orders for buyers:', error);
    throw error;
  }
};

/**
 * Make a purchase as a buyer (for marketplace orders where seller is looking for buyer)
 */
export const makePurchaseAsBuyer = async (
  orderId: string,
): Promise<MakePurchaseAsBuyerResponse> => {
  try {
    const makePurchaseAsBuyerFunction = httpsCallable<
      { buyerId: string; orderId: string },
      MakePurchaseAsBuyerResponse
    >(firebaseFunctions, 'makePurchaseAsBuyer');

    // Get current user ID from Firebase Auth
    const auth = await import('firebase/auth');
    const currentUser = auth.getAuth().currentUser;

    if (!currentUser) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await makePurchaseAsBuyerFunction({
      buyerId: currentUser.uid,
      orderId,
    });

    // Cache will be cleared by components using this function

    return result.data;
  } catch (error) {
    console.error('Error making purchase as buyer:', error);
    throw error;
  }
};

/**
 * Clear buyer orders cache - to be called from components with cache access
 */
export const clearBuyerOrdersCache = () => {
  // This will be implemented by components that have access to the cache context
};
