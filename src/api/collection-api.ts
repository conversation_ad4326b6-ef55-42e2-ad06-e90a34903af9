import type { DocumentSnapshot } from 'firebase/firestore';
import {
  collection,
  deleteDoc,
  doc,
  getDocs,
  limit,
  orderBy,
  query,
  setDoc,
  startAfter,
  updateDoc,
  writeBatch,
} from 'firebase/firestore';

import type { Collection } from '@/core.constants';
import { firestore } from '@/root-context';
import { globalCache } from '@/utils/cache-utils';

const COLLECTION_NAME = 'collections';
const CACHE_CONFIG = { duration: 24 * 60 * 60 * 1000 }; // 1 day

export const getAllCollections = async (): Promise<Collection[]> => {
  const cachedCollections = globalCache.getCollections<Collection>();
  if (cachedCollections) {
    return cachedCollections.sort((a: Collection, b: Collection) =>
      a.name.localeCompare(b.name),
    );
  }

  try {
    const snapshot = await getDocs(
      query(collection(firestore, COLLECTION_NAME), orderBy('name', 'asc')),
    );

    const collections: Collection[] = [];
    snapshot.forEach((doc) => {
      collections.push({ id: doc.id, ...doc.data() } as Collection);
    });

    globalCache.setCollections(collections, CACHE_CONFIG);

    return collections;
  } catch (error) {
    console.error('Error fetching all collections:', error);
    throw error;
  }
};

export const getCollectionById = async (
  id: string,
): Promise<Collection | null> => {
  try {
    const allCollections = await getAllCollections();
    const foundCollection = allCollections.find(
      (collection) => collection.id === id,
    );

    return foundCollection || null;
  } catch (error) {
    console.error('Error fetching collection by ID:', error);
    throw error;
  }
};

export const createCollection = async (collectionData: Collection) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, collectionData.id);
    await setDoc(docRef, {
      ...collectionData,
    });

    globalCache.invalidate('collections:all');

    return collectionData.id;
  } catch (error) {
    console.error('Error creating collection:', error);
    throw error;
  }
};

export const updateCollection = async (
  id: string,
  collectionData: Partial<Collection>,
) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await updateDoc(docRef, {
      ...collectionData,
    });

    globalCache.invalidate('collections:all');
  } catch (error) {
    console.error('Error updating collection:', error);
    throw error;
  }
};

export const deleteCollection = async (id: string) => {
  try {
    const docRef = doc(firestore, COLLECTION_NAME, id);
    await deleteDoc(docRef);

    globalCache.invalidate('collections:all');
  } catch (error) {
    console.error('Error deleting collection:', error);
    throw error;
  }
};

export const getCollections = async (
  pageSize: number = 10,
  lastDoc?: DocumentSnapshot,
) => {
  try {
    if (!lastDoc && pageSize >= 100) {
      const cachedCollections = globalCache.getCollections<Collection>();
      if (cachedCollections) {
        const limitedCollections = cachedCollections
          .sort((a: Collection, b: Collection) => a.name.localeCompare(b.name))
          .slice(0, pageSize);

        return {
          collections: limitedCollections,
          lastDoc: undefined,
          hasMore:
            limitedCollections.length === pageSize &&
            cachedCollections.length > pageSize,
        };
      }
    }

    let q = query(
      collection(firestore, COLLECTION_NAME),
      orderBy('name', 'asc'),
      limit(pageSize),
    );

    if (lastDoc) {
      q = query(
        collection(firestore, COLLECTION_NAME),
        orderBy('name', 'asc'),
        startAfter(lastDoc),
        limit(pageSize),
      );
    }

    const snapshot = await getDocs(q);
    const collections: Collection[] = [];

    snapshot.forEach((doc) => {
      collections.push({ id: doc.id, ...doc.data() } as Collection);
    });

    updateCache(collections);

    if (!lastDoc && pageSize >= 100 && collections.length < pageSize) {
      allCollectionsCached = true;
    }

    return {
      collections,
      lastDoc: snapshot.docs[snapshot.docs.length - 1],
      hasMore: snapshot.docs.length === pageSize,
    };
  } catch (error) {
    console.error('Error fetching collections:', error);
    throw error;
  }
};

export const clearAllCollections = async () => {
  try {
    const snapshot = await getDocs(collection(firestore, COLLECTION_NAME));
    const batch = writeBatch(firestore);

    snapshot.docs.forEach((doc) => {
      batch.delete(doc.ref);
    });

    await batch.commit();
    console.log(`Deleted ${snapshot.docs.length} collections`);

    clearCache();
  } catch (error) {
    console.error('Error clearing collections:', error);
    throw error;
  }
};

export const createBulkCollections = async (
  collections: Omit<Collection, 'id'>[],
) => {
  try {
    const batch = writeBatch(firestore);
    const collectionsCollection = collection(firestore, COLLECTION_NAME);

    collections.forEach((collectionData) => {
      const docRef = doc(collectionsCollection);
      batch.set(docRef, {
        ...collectionData,
      });
    });

    await batch.commit();
    console.log(`Created ${collections.length} collections`);

    clearCache();
  } catch (error) {
    console.error('Error creating bulk collections:', error);
    throw error;
  }
};

export const clearCollectionsCache = () => {
  globalCache.invalidate('collections:all');
};
