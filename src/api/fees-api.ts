import { doc, getDoc, setDoc } from 'firebase/firestore';

import { firestore } from '@/root-context';

import type { AppConfig } from './app-config-api';

const APP_CONFIG_COLLECTION = 'app_config';
const APP_CONFIG_DOC_ID = 'fees';

/**
 * Simple cache for fees config
 *
 * Features:
 * - Caches fees config for 5 minutes to reduce Firebase reads
 * - Automatically invalidates cache on update operations
 * - Use clearFeesConfigCache() to manually clear cache if needed
 */
let cachedFeesConfig: AppConfig | null = null;
let feesCacheTimestamp = 0;
const FEES_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes in milliseconds

// Cache management functions
const isFeesConfigCacheValid = () => {
  return Date.now() - feesCacheTimestamp < FEES_CACHE_DURATION;
};

const clearFeesConfigCache = () => {
  cachedFeesConfig = null;
  feesCacheTimestamp = 0;
};

const updateFeesConfigCache = (config: AppConfig) => {
  cachedFeesConfig = config;
  feesCacheTimestamp = Date.now();
};

const getCachedFeesConfig = (): AppConfig | null => {
  if (!isFeesConfigCacheValid()) {
    clearFeesConfigCache();
    return null;
  }
  return cachedFeesConfig;
};

export const loadFeesConfig = async (): Promise<AppConfig | null> => {
  const cachedConfig = getCachedFeesConfig();
  if (cachedConfig) {
    return cachedConfig;
  }

  try {
    const configDoc = await getDoc(
      doc(firestore, APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID),
    );

    if (configDoc.exists()) {
      const config = configDoc.data() as AppConfig;
      updateFeesConfigCache(config);
      return config;
    }

    return null;
  } catch (error) {
    console.error('Error loading fees config:', error);
    throw error;
  }
};

export const updateFeesConfig = async (config: AppConfig): Promise<void> => {
  try {
    await setDoc(
      doc(firestore, APP_CONFIG_COLLECTION, APP_CONFIG_DOC_ID),
      config,
    );

    clearFeesConfigCache();
  } catch (error) {
    console.error('Error updating fees config:', error);
    throw error;
  }
};

export const clearFeesCache = () => {
  clearFeesConfigCache();
};
