import type { DocumentSnapshot } from 'firebase/firestore';
import {
  collection,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import type { OrderEntity } from '@/core.constants';
// Cache will be managed by components that call these functions
import { firebaseFunctions, firestore } from '@/root-context';

const COLLECTION_NAME = 'orders';

export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';
  limit?: number;
  lastDoc?: DocumentSnapshot | null;
  currentUserId?: string;
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null;
  hasMore: boolean;
}

export interface MakePurchaseAsSellerResponse {
  success: boolean;
  message: string;
  lockedAmount: number;
  orderAmount: number;
  lockPercentage: number;
}

export const getOrdersForSellers = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    // Cache will be handled by components using this function
    const pageSize = filters.limit ?? 20;
    console.log('🔍 getOrdersForSellers called with filters:', filters);

    // For sellers tab: get orders where buyerId exists and sellerId is null/doesn't exist (buyers looking for sellers)
    let q = query(
      collection(firestore, COLLECTION_NAME),
      where('status', '==', 'active'),
      where('buyerId', '!=', null),
    );

    if (filters.collectionId) {
      q = query(q, where('collectionId', '==', filters.collectionId));
    }

    // Apply sorting
    if (filters.sortBy) {
      if (filters.sortBy === 'price_asc') {
        q = query(q, orderBy('amount', 'asc'));
      } else if (filters.sortBy === 'price_desc') {
        q = query(q, orderBy('amount', 'desc'));
      } else if (filters.sortBy === 'date_asc') {
        q = query(q, orderBy('createdAt', 'asc'));
      } else {
        q = query(q, orderBy('createdAt', 'desc'));
      }
    } else {
      q = query(q, orderBy('createdAt', 'desc'));
    }

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }
    q = query(q, limit(pageSize + 1)); // Get one extra to check if there are more

    const snapshot = await getDocs(q);
    console.log('📊 Raw query returned', snapshot.docs.length, 'documents');
    let orders: OrderEntity[] = [];
    let lastDoc = null;
    let hasMore = false;

    const docs = snapshot.docs;
    let processedCount = 0;

    for (const element of docs) {
      const doc = element;

      if (processedCount >= pageSize) {
        hasMore = true; // There's at least one more document
        break;
      }

      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;
      console.log('📋 Order data:', {
        id: orderData.id,
        buyerId: orderData.buyerId,
        sellerId: orderData.sellerId,
        status: orderData.status,
      });

      // For sellers tab: only include orders where sellerId is null or doesn't exist
      // Also exclude orders where current user is the buyer
      if (
        !orderData.sellerId &&
        (!filters.currentUserId || orderData.buyerId !== filters.currentUserId)
      ) {
        orders.push(orderData);
        lastDoc = doc; // Only set lastDoc when we actually include an order
        processedCount++;
        console.log('✅ Order included for sellers tab');
      } else {
        console.log(
          '❌ Order excluded (has sellerId or belongs to current user)',
        );
      }
    }

    // Apply price filters client-side
    if (filters.minPrice !== undefined) {
      orders = orders.filter((order) => order.amount >= filters.minPrice!);
    }
    if (filters.maxPrice !== undefined) {
      orders = orders.filter((order) => order.amount <= filters.maxPrice!);
    }

    console.log('🎯 Final result for sellers:', {
      ordersCount: orders.length,
      hasMore,
      lastDoc: !!lastDoc,
    });

    const result = {
      orders,
      lastDoc,
      hasMore,
    };

    // Cache will be handled by components using this function

    return result;
  } catch (error) {
    console.error('Error fetching orders for sellers:', error);
    throw error;
  }
};

/**
 * Make a purchase as a seller (for marketplace orders where buyer is looking for seller)
 */
export const makePurchaseAsSeller = async (
  orderId: string,
): Promise<MakePurchaseAsSellerResponse> => {
  try {
    const makePurchaseAsSellerFunction = httpsCallable<
      { sellerId: string; orderId: string },
      MakePurchaseAsSellerResponse
    >(firebaseFunctions, 'makePurchaseAsSeller');

    // Get current user ID from Firebase Auth
    const auth = await import('firebase/auth');
    const currentUser = auth.getAuth().currentUser;

    if (!currentUser) {
      throw new Error('User must be authenticated to make a purchase');
    }

    const result = await makePurchaseAsSellerFunction({
      sellerId: currentUser.uid,
      orderId,
    });

    // Cache will be cleared by components using this function

    return result.data;
  } catch (error) {
    console.error('Error making purchase as seller:', error);
    throw error;
  }
};

/**
 * Clear seller orders cache - to be called from components with cache access
 */
export const clearSellerOrdersCache = () => {
  // This will be implemented by components that have access to the cache context
};
