import type { DocumentSnapshot } from 'firebase/firestore';
import {
  collection,
  getDocs,
  limit,
  orderBy,
  query,
  startAfter,
  where,
} from 'firebase/firestore';
import { httpsCallable } from 'firebase/functions';

import type { OrderEntity } from '@/core.constants';
import { useAppCache } from '@/contexts/AppCacheContext';
import { firebaseFunctions, firestore } from '@/root-context';

const COLLECTION_NAME = 'orders';

export interface OrderFilters {
  minPrice?: number;
  maxPrice?: number;
  collectionId?: string;
  sortBy?: 'price_asc' | 'price_desc' | 'date_asc' | 'date_desc';
  limit?: number;
  lastDoc?: DocumentSnapshot | null;
  currentUserId?: string;
}

export interface PaginatedOrdersResult {
  orders: OrderEntity[];
  lastDoc: DocumentSnapshot | null;
  hasMore: boolean;
}

export interface MakeSecondaryMarketPurchaseResponse {
  success: boolean;
  message: string;
  orderId: string;
  secondaryMarketPrice: number;
  newBuyerId: string;
  oldBuyerId: string;
  netAmountToOldBuyer: number;
  feeAmount: number;
  lockedAmount: number;
}

export const getSecondaryMarketOrders = async (
  filters: OrderFilters = {},
): Promise<PaginatedOrdersResult> => {
  try {
    const cache = useAppCache();
    const cacheKey = cache.generateKey('getSecondaryMarketOrders', filters);
    
    // Check cache first (including pagination)
    const cachedResult = cache.getPaginatedResult<OrderEntity>(cacheKey, 0);
    if (cachedResult) {
      console.log('🎯 Returning cached result for getSecondaryMarketOrders');
      return {
        orders: cachedResult.data,
        lastDoc: cachedResult.lastDoc,
        hasMore: cachedResult.hasMore,
      };
    }

    const pageSize = filters.limit || 20;
    console.log('🔍 getSecondaryMarketOrders called with filters:', filters);

    // For secondary market: get orders where status is paid and secondaryMarketPrice > 0
    // We'll filter for buyerId and sellerId existence client-side to avoid multiple != filters
    let q = query(
      collection(firestore, COLLECTION_NAME),
      where('status', '==', 'paid'),
      where('secondaryMarketPrice', '>', 0),
    );

    // Apply collection filter
    if (filters.collectionId) {
      q = query(q, where('collectionId', '==', filters.collectionId));
    }

    // Apply sorting - for secondary market, we'll sort by secondaryMarketPrice or createdAt
    if (filters.sortBy) {
      if (filters.sortBy === 'price_asc') {
        q = query(q, orderBy('secondaryMarketPrice', 'asc'));
      } else if (filters.sortBy === 'price_desc') {
        q = query(q, orderBy('secondaryMarketPrice', 'desc'));
      } else if (filters.sortBy === 'date_asc') {
        q = query(q, orderBy('createdAt', 'asc'));
      } else {
        q = query(q, orderBy('createdAt', 'desc'));
      }
    } else {
      q = query(q, orderBy('createdAt', 'desc'));
    }

    // Add pagination
    if (filters.lastDoc) {
      q = query(q, startAfter(filters.lastDoc));
    }
    q = query(q, limit(pageSize + 1)); // Get one extra to check if there are more

    const snapshot = await getDocs(q);
    let orders: OrderEntity[] = [];
    let lastDoc = null;
    let hasMore = false;

    const docs = snapshot.docs;
    let processedCount = 0;

    for (let index = 0; index < docs.length; index++) {
      const doc = docs[index];

      if (processedCount >= pageSize) {
        hasMore = true; // There's at least one more document
        break;
      }

      const orderData = { id: doc.id, ...doc.data() } as OrderEntity;

      // Filter client-side: only include orders where both buyerId and sellerId exist
      // Also exclude orders where current user is either the buyer or seller
      if (
        filters.currentUserId &&
        (orderData.buyerId === filters.currentUserId ||
          orderData.sellerId === filters.currentUserId)
      ) {
        orders.push(orderData);
        lastDoc = doc;
        processedCount++;
      }
    }

    console.log('🔍 Raw orders from Firestore for secondary market:', {
      count: orders.length,
      hasMore,
      lastDoc: !!lastDoc,
    });

    // Apply price filters client-side (using secondaryMarketPrice for secondary market)
    if (filters.minPrice !== undefined) {
      orders = orders.filter(
        (order) => (order.secondaryMarketPrice || 0) >= filters.minPrice!,
      );
    }
    if (filters.maxPrice !== undefined) {
      orders = orders.filter(
        (order) => (order.secondaryMarketPrice || 0) <= filters.maxPrice!,
      );
    }

    console.log('🎯 Final result for secondary market:', {
      ordersCount: orders.length,
      hasMore,
      lastDoc: !!lastDoc,
    });

    const result = {
      orders,
      lastDoc,
      hasMore,
    };

    // Cache the result (including pagination)
    cache.setPaginatedResult(cacheKey, orders, lastDoc, hasMore);

    return result;
  } catch (error) {
    console.error('Error fetching secondary market orders:', error);
    throw error;
  }
};

/**
 * Purchase an order from secondary market
 */
export const makeSecondaryMarketPurchase = async (
  orderId: string,
): Promise<MakeSecondaryMarketPurchaseResponse> => {
  try {
    const makeSecondaryMarketPurchaseFunction = httpsCallable<
      { orderId: string },
      MakeSecondaryMarketPurchaseResponse
    >(firebaseFunctions, 'makeSecondaryMarketPurchase');

    const result = await makeSecondaryMarketPurchaseFunction({ orderId });

    // Clear cache after secondary market purchase
    const cache = useAppCache();
    cache.invalidatePattern('getOrdersForBuyers');
    cache.invalidatePattern('getOrdersForSellers');
    cache.invalidatePattern('getSecondaryMarketOrders');

    return result.data;
  } catch (error) {
    console.error('Error making secondary market purchase:', error);
    throw error;
  }
};

/**
 * Clear secondary market orders cache
 */
export const clearSecondaryMarketOrdersCache = () => {
  const cache = useAppCache();
  cache.invalidatePattern('getSecondaryMarketOrders');
};
