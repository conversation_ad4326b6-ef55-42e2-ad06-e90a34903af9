'use client';

import { Input as TgInput, Select, Spinner } from '@telegram-apps/telegram-ui';
import type { DocumentSnapshot } from 'firebase/firestore';
import { Loader2 } from 'lucide-react';
import { useCallback, useEffect, useRef, useState } from 'react';

import { clearOrdersCache, getSecondaryMarketOrders } from '@/api/order-api';
import { CollectionSelect } from '@/components/ui/collection-select';
import { ItemCacheProvider } from '@/components/ui/virtualized-grid';
import type { OrderEntity } from '@/core.constants';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { useRootContext } from '@/root-context';

import { SecondaryOrderDetailsDrawer } from './secondary-order-details-drawer';
import { VirtualizedSecondaryMarketOrderCard } from './virtualized-secondary-market-order-card';

export default function MarketplacePage() {
  const { collections, refetchUser, currentUser } = useRootContext();
  const [showOrderDetailsDrawer, setShowOrderDetailsDrawer] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderEntity | null>(null);
  const [buyersOrders, setBuyersOrders] = useState<OrderEntity[]>([]);
  const [buyersLoading, setBuyersLoading] = useState(false);
  const [buyersLoadingMore, setBuyersLoadingMore] = useState(false);
  const [buyersHasMore, setBuyersHasMore] = useState(true);
  const [buyersLastDoc, setBuyersLastDoc] = useState<DocumentSnapshot | null>(
    null,
  );

  // Use refs to store current lastDoc values to avoid stale state issues
  const buyersLastDocRef = useRef<DocumentSnapshot | null>(null);

  // Filters
  const [minPrice, setMinPrice] = useState('');
  const [maxPrice, setMaxPrice] = useState('');
  const [selectedCollection, setSelectedCollection] = useState<string>('all');
  const [sortBy, setSortBy] = useState<
    'price_asc' | 'price_desc' | 'date_asc' | 'date_desc'
  >('date_desc');

  const loadOrders = useCallback(
    async (reset = true) => {
      console.log('🔄 loadOrders called with reset:', reset);

      if (reset) {
        setBuyersLoading(true);
        setBuyersOrders([]);
        setBuyersLastDoc(null);
        buyersLastDocRef.current = null;
        setBuyersHasMore(true);
      } else {
        setBuyersLoadingMore(true);
      }

      try {
        // For reset, always use null. For pagination, use ref values to avoid stale state
        const currentLastDoc = reset ? null : buyersLastDocRef.current;

        console.log('🔍 loadOrders debug:', {
          reset,
          buyersLastDoc: buyersLastDoc?.id || 'null',
          buyersLastDocRef: buyersLastDocRef.current?.id || 'null',
          currentLastDoc: currentLastDoc?.id || 'null',
          ordersCount: buyersOrders.length,
        });

        // Ensure sortBy is a valid string value
        const validSortBy =
          typeof sortBy === 'string' &&
          ['price_asc', 'price_desc', 'date_asc', 'date_desc'].includes(sortBy)
            ? sortBy
            : 'date_desc';

        const filters = {
          minPrice: minPrice ? parseFloat(minPrice) : undefined,
          maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,
          collectionId:
            selectedCollection !== 'all' ? selectedCollection : undefined,
          sortBy: validSortBy,
          limit: 3,
          lastDoc: currentLastDoc,
          currentUserId: currentUser?.id,
        };

        const result = await getSecondaryMarketOrders(filters);

        if (reset) {
          console.log(
            '🔄 Resetting buyers orders with',
            result.orders.length,
            'orders',
          );
          setBuyersOrders(result.orders);
        } else {
          // Deduplicate orders to prevent duplicate keys
          setBuyersOrders((prev) => {
            const existingIds = new Set(prev.map((order) => order.id));
            const newOrders = result.orders.filter(
              (order) => !existingIds.has(order.id),
            );
            const duplicates = result.orders.filter((order) =>
              existingIds.has(order.id),
            );

            if (duplicates.length > 0) {
              console.warn(
                '🚨 Found duplicate orders for buyers:',
                duplicates.map((o) => o.id),
              );
            }

            console.log(
              '➕ Adding',
              newOrders.length,
              'new buyers orders, filtered out',
              duplicates.length,
              'duplicates',
            );
            return [...prev, ...newOrders];
          });
        }
        console.log('🔍 Setting buyersLastDoc:', result.lastDoc?.id || 'null');
        setBuyersLastDoc(result.lastDoc);
        buyersLastDocRef.current = result.lastDoc;
        setBuyersHasMore(result.hasMore);
      } catch (error) {
        console.error('Error loading orders:', error);
      } finally {
        setBuyersLoading(false);
        setBuyersLoadingMore(false);
      }
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [minPrice, maxPrice, selectedCollection, sortBy],
  );

  // Single load more function that handles both tabs
  const loadMoreOrders = useCallback(() => {
    console.log('🔄 loadMoreOrders called, activeTab:');
    const hasMore = buyersHasMore;
    const loading = buyersLoading || buyersLoadingMore;

    console.log('� Load more state:', { hasMore, loading });

    if (hasMore && !loading) {
      console.log('✅ Loading more orders...');
      loadOrders(false);
    } else {
      console.log('❌ Cannot load more:', { hasMore, loading });
    }
  }, [buyersHasMore, buyersLoading, buyersLoadingMore, loadOrders]);

  const buyersLoadMoreRef = useInfiniteScroll({
    hasMore: buyersHasMore,
    loading: buyersLoading || buyersLoadingMore,
    onLoadMore: () => {
      console.log('🔄 Buyers infinite scroll triggered');
      loadMoreOrders();
    },
  });

  useEffect(() => {
    loadOrders(true);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [minPrice, maxPrice, selectedCollection, sortBy]);

  const handleOrderClick = (order: OrderEntity) => {
    setSelectedOrder(order);
    setShowOrderDetailsDrawer(true);
  };

  const handleOrderAction = () => {
    clearOrdersCache(); // Clear cache to ensure fresh data
    loadOrders(true); // Refresh orders after action
    refetchUser(); // Refresh user data to update balance
  };

  return (
    <div className="space-y-2 bg-[#17212b] min-h-screen">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-[#f5f5f5]">
          Secondary marketplace
        </h1>
      </div>

      <div className="flex flex-wrap items-end gap-2 py-3 rounded-lg">
        <div className="flex-1 min-w-[100px] xss:min-w-[120px]">
          <div className="[&>div]:p-0!">
            <TgInput
              type="number"
              header="Min"
              placeholder="0"
              value={minPrice}
              onChange={(e) => {
                const value = e.target.value;
                // Allow empty string or valid numbers (including decimals)
                if (value === '' || /^\d*\.?\d*$/.test(value)) {
                  setMinPrice(value);
                }
              }}
              min="0"
              step="0.01"
              className="text-white text-sm h-9 [&+h6]:-top-[12px]!"
            />
          </div>
        </div>
        <div className="flex-1 min-w-[100px] xss:min-w-[120px]">
          <div className="[&>div]:p-0!">
            <TgInput
              type="number"
              header="Max"
              placeholder="0"
              value={maxPrice}
              onChange={(e) => {
                const value = e.target.value;
                // Allow empty string or valid numbers (including decimals)
                if (value === '' || /^\d*\.?\d*$/.test(value)) {
                  setMaxPrice(value);
                }
              }}
              min="0"
              step="0.01"
              className="text-white text-sm h-9 [&+h6]:-top-[12px]!"
            />
          </div>
        </div>

        <div className="flex-1 min-w-[120px] xss:min-w-[140px] mt-2">
          <CollectionSelect
            animated
            collections={collections}
            value={selectedCollection}
            onValueChange={setSelectedCollection}
            placeholder="All Collections"
          />
        </div>
        <div className="flex-1 min-w-[120px] xss:min-w-[140px] mt-2">
          <div className="[&>div]:p-0!">
            <Select
              header="Sort by"
              value={sortBy}
              onChange={(e) => {
                const value = e.target.value as
                  | 'price_asc'
                  | 'price_desc'
                  | 'date_asc'
                  | 'date_desc';
                setSortBy(value);
              }}
              className="[&>select]:px-[12px]! [&>select]:py-[6px]! [&+h6]:-top-[12px]!"
            >
              <option value={'date_desc'}>Newest First</option>
              <option value={'date_asc'}>Oldest First</option>
              <option value={'price_desc'}>Price: High to Low</option>
              <option value={'price_asc'}>Price: Low to High</option>
            </Select>
          </div>
        </div>
      </div>

      {buyersLoading ? (
        <div className="text-center py-8">
          <Spinner className="flex justify-center" size="l" />
          <p className="text-[#708499] mt-2">Loading orders...</p>
        </div>
      ) : buyersOrders.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-[#708499]">No orders found for buyers</p>
        </div>
      ) : (
        <ItemCacheProvider>
          <div className="grid grid-cols-2 xl:grid-cols-4 gap-2">
            {buyersOrders.map((order, index) => (
              <VirtualizedSecondaryMarketOrderCard
                animated
                key={`buyers-${order.id}-${index}`}
                order={order}
                collection={collections.find(
                  (c) => c.id === order.collectionId,
                )}
                onClick={() => handleOrderClick(order)}
                index={index}
                initialRenderedCount={8}
              />
            ))}
          </div>
        </ItemCacheProvider>
      )}

      <div
        ref={buyersLoadMoreRef}
        className="flex justify-center py-4 w-full"
        style={{
          height: '60px',
          minHeight: '60px',
          backgroundColor: 'transparent',
        }}
      >
        {buyersLoadingMore && (
          <div className="flex items-center gap-2 text-gray-400">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span>Loading more orders...</span>
          </div>
        )}
      </div>

      <SecondaryOrderDetailsDrawer
        open={showOrderDetailsDrawer}
        onOpenChange={setShowOrderDetailsDrawer}
        order={selectedOrder}
        onOrderAction={handleOrderAction}
      />
    </div>
  );
}
