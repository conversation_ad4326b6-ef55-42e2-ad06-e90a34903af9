'use client';

import { useTelegramAuth } from '@/hooks/useTelegramAuth';

interface TelegramAuthProps {
  onSuccess?: () => void;
  onError?: (error: string) => void;
}

export const TelegramAuth = ({ onSuccess, onError }: TelegramAuthProps) => {
  const { authenticate, isLoading, isInTelegram } = useTelegramAuth({
    onSuccess,
    onError,
  });

  if (!isInTelegram) {
    return <div className="text-center p-4">Not in Telegram</div>;
  }

  return (
    <div className="text-center p-4">
      <button
        onClick={authenticate}
        disabled={isLoading}
        className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
      >
        {isLoading ? (
          <>
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            Signing in...
          </>
        ) : (
          'Sign in with Telegram'
        )}
      </button>
    </div>
  );
};

export default TelegramAuth;
