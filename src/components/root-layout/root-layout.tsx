import '../../app/globals.css';

import { TonConnectUIProvider } from '@tonconnect/ui-react';
import type { Metadata } from 'next';
import { <PERSON>eist, <PERSON>eist_Mono } from 'next/font/google';
import { Toaster } from 'sonner';

import { Root } from '@/components/Root';
import { RootProvider } from '@/root-context';
import { WALLET_MANIFEST_URL } from '@/utils/ton-constants';

import { RootLayoutHeader } from '../root-layout-header';
import RootLayoutFooter from './root-layout-footer';

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

if (!Object.hasOwn) {
  Object.hasOwn = function (obj, prop) {
    return Object.prototype.hasOwnProperty.call(obj, prop);
  };
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="dark" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased bg-[#17212b] text-[#f5f5f5]`}
        suppressHydrationWarning
      >
        <RootProvider>
          <Root>
            <TonConnectUIProvider manifestUrl={WALLET_MANIFEST_URL}>
              <RootLayoutHeader />
              <div className="min-h-screen flex flex-col pt-[72px] pb-16">
                <main className="flex-1 p-2">
                  <div className="max-w-6xl mx-auto">{children}</div>
                </main>
              </div>
              <Toaster
                theme="dark"
                toastOptions={{
                  style: {
                    background: '#232e3c',
                    border: '1px solid #3a4a5c',
                    color: '#f5f5f5',
                  },
                }}
              />
              <RootLayoutFooter />
            </TonConnectUIProvider>
          </Root>
        </RootProvider>
      </body>
    </html>
  );
}
