'use client';

import { useState } from 'react';
import { toast } from 'sonner';

import {
  getUserById,
  getUserStats,
  searchUsers,
  updateUser,
  updateUserBalance,
  updateUserTelegram,
  updateUserWallet,
} from '@/api/user-api';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import type { UserBalance, UserEntity } from '@/core.constants';

export default function UserApiExample() {
  const [userId, setUserId] = useState('');
  const [user, setUser] = useState<UserEntity | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<UserEntity[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [stats, setStats] = useState<any>(null);

  // Update user basic info (using cloud function)
  const handleUpdateUser = async () => {
    if (!userId) {
      toast.error('Please enter a user ID');
      return;
    }

    setIsLoading(true);
    try {
      const updatedUser = await updateUser(userId, {
        displayName: 'Updated Name via Cloud Function',
        tg_id: '123456789',
      });
      setUser(updatedUser);
      toast.success('User updated successfully via cloud function');
    } catch (error) {
      toast.error(`Error updating user: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Get user by ID
  const handleGetUser = async () => {
    if (!userId) {
      toast.error('Please enter a user ID');
      return;
    }

    setIsLoading(true);
    try {
      const userData = await getUserById(userId);
      setUser(userData);
      if (userData) {
        toast.success('User found');
      } else {
        toast.error('User not found');
      }
    } catch (error) {
      toast.error(`Error getting user: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Update user balance
  const handleUpdateBalance = async () => {
    if (!userId) {
      toast.error('Please enter a user ID');
      return;
    }

    setIsLoading(true);
    try {
      const newBalance: UserBalance = {
        sum: 100.5,
        locked: 10.25,
      };

      const updatedUser = await updateUserBalance(userId, newBalance);
      setUser(updatedUser);
      toast.success('User balance updated');
    } catch (error) {
      toast.error(`Error updating balance: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Update user wallet (with both addresses)
  const handleUpdateWallet = async () => {
    if (!userId) {
      toast.error('Please enter a user ID');
      return;
    }

    setIsLoading(true);
    try {
      const updatedUser = await updateUserWallet(
        userId,
        'UQBzm-T_1XfNZKkKA0CtkkTu9E2uEGt8le7oZ1k1p1CW5LV1', // User-friendly address
        '0:739be4ffd577cd64a90a0340ad9244eef44dae106b7c95eee8675935a75096e4', // Raw address
      );
      setUser(updatedUser);
      toast.success('User wallet updated with both addresses');
    } catch (error) {
      toast.error(`Error updating wallet: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Update user Telegram info
  const handleUpdateTelegram = async () => {
    if (!userId) {
      toast.error('Please enter a user ID');
      return;
    }

    setIsLoading(true);
    try {
      const updatedUser = await updateUserTelegram(
        userId,
        '123456789',
        'John Doe',
        'https://example.com/photo.jpg',
      );
      setUser(updatedUser);
      toast.success('User Telegram info updated');
    } catch (error) {
      toast.error(`Error updating Telegram info: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Search users
  const handleSearchUsers = async () => {
    if (!searchTerm) {
      toast.error('Please enter a search term');
      return;
    }

    setIsLoading(true);
    try {
      const results = await searchUsers(searchTerm, 10);
      setSearchResults(results);
      toast.success(`Found ${results.length} users`);
    } catch (error) {
      toast.error(`Error searching users: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  // Get user statistics
  const handleGetStats = async () => {
    setIsLoading(true);
    try {
      const userStats = await getUserStats();
      setStats(userStats);
      toast.success('Statistics loaded');
    } catch (error) {
      toast.error(`Error getting statistics: ${error}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 space-y-6">
      <h1 className="text-3xl font-bold">User API Examples</h1>

      <Card>
        <CardHeader>
          <CardTitle>User Operations</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="userId">User ID</Label>
            <Input
              id="userId"
              value={userId}
              onChange={(e) => setUserId(e.target.value)}
              placeholder="Enter user ID"
            />
          </div>

          <div className="flex flex-wrap gap-2">
            <Button onClick={handleGetUser} disabled={isLoading}>
              Get User
            </Button>
            <Button onClick={handleUpdateUser} disabled={isLoading}>
              Update User Info
            </Button>
            <Button onClick={handleUpdateBalance} disabled={isLoading}>
              Update Balance
            </Button>
            <Button onClick={handleUpdateWallet} disabled={isLoading}>
              Update Wallet
            </Button>
            <Button onClick={handleUpdateTelegram} disabled={isLoading}>
              Update Telegram
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Search Users</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="searchTerm">Search Term</Label>
            <Input
              id="searchTerm"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Enter name or email"
            />
          </div>

          <Button onClick={handleSearchUsers} disabled={isLoading}>
            Search Users
          </Button>

          {searchResults.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-semibold">Search Results:</h3>
              {searchResults.map((user) => (
                <div key={user.id} className="p-2 border rounded">
                  <p>
                    <strong>ID:</strong> {user.id}
                  </p>
                  <p>
                    <strong>Name:</strong> {user.displayName || 'N/A'}
                  </p>
                  <p>
                    <strong>Email:</strong> {user.email || 'N/A'}
                  </p>
                  <p>
                    <strong>Role:</strong> {user.role || 'N/A'}
                  </p>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>User Statistics</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <Button onClick={handleGetStats} disabled={isLoading}>
            Get Statistics
          </Button>

          {stats && (
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="p-3 border rounded">
                <p className="text-sm text-gray-600">Total Users</p>
                <p className="text-2xl font-bold">{stats.totalUsers}</p>
              </div>
              <div className="p-3 border rounded">
                <p className="text-sm text-gray-600">Admin Users</p>
                <p className="text-2xl font-bold">{stats.adminUsers}</p>
              </div>
              <div className="p-3 border rounded">
                <p className="text-sm text-gray-600">Regular Users</p>
                <p className="text-2xl font-bold">{stats.regularUsers}</p>
              </div>
              <div className="p-3 border rounded">
                <p className="text-sm text-gray-600">Users with Wallets</p>
                <p className="text-2xl font-bold">{stats.usersWithWallets}</p>
              </div>
              <div className="p-3 border rounded">
                <p className="text-sm text-gray-600">Users with Telegram</p>
                <p className="text-2xl font-bold">{stats.usersWithTelegram}</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {user && (
        <Card>
          <CardHeader>
            <CardTitle>Current User Data</CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="bg-gray-100 p-4 rounded overflow-auto">
              {JSON.stringify(user, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
